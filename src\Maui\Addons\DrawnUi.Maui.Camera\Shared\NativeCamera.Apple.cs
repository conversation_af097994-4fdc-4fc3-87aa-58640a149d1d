#if IOS || MACCATALYST
using System.ComponentModel;
using System.Runtime.CompilerServices;
using AppoMobi.Specials;
using AVFoundation;
using CoreFoundation;
using CoreGraphics;
using CoreImage;
using CoreMedia;
using CoreVideo;
using DrawnUi.Controls;
using Foundation;
using ImageIO;
using Microsoft.Maui.Media;
using Photos;
using SkiaSharp;
using SkiaSharp.Views.iOS;
using UIKit;
using Metal;
using MetalKit;
using static AVFoundation.AVMetadataIdentifiers;

namespace DrawnUi.Camera;

public partial class NativeCamera : NSObject, IDisposable, INativeCamera, INotifyPropertyChanged, IAVCaptureVideoDataOutputSampleBufferDelegate
{
    /// <summary>
    /// GPU-backed frame container using Metal textures for optimal performance
    /// </summary>
    public class MetalFrameData : IDisposable
    {
        public CVMetalTexture MetalTexture { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public DateTime Time { get; set; }
        public Rotation CurrentRotation { get; set; }
        public CameraPosition Facing { get; set; }
        public int Orientation { get; set; }
        public SKImage PreprocessedImage { get; set; } // Pre-rotated, GPU-backed SKImage

        public void Dispose()
        {
            PreprocessedImage?.Dispose();
            PreprocessedImage = null;
            MetalTexture?.Dispose();
            MetalTexture = null;
        }
    }

    protected readonly SkiaCamera FormsControl;
    private AVCaptureSession _session;
    private AVCaptureVideoDataOutput _videoDataOutput;
    private AVCaptureStillImageOutput _stillImageOutput;
    private AVCaptureDeviceInput _deviceInput;
    private DispatchQueue _videoDataOutputQueue;
    private CameraProcessorState _state = CameraProcessorState.None;
    private bool _flashSupported;
    private bool _isCapturingStill;
    private double _zoomScale = 1.0;
    private readonly object _lockPreview = new();
    private CapturedImage _preview;
    bool _cameraUnitInitialized;

    // Frame processing throttling - prevent concurrent processing
    private volatile bool _isProcessingFrame = false;
    private int _skippedFrameCount = 0;
    private int _processedFrameCount = 0;

    // Metal GPU acceleration components
    private readonly object _lockMetalFrame = new();
    private MetalFrameData _latestMetalFrame;
    private MetalFrameData _oldMetalFrame;
    private IMTLDevice _metalDevice;
    private CVMetalTextureCache _metalTextureCache;
    private GRContext _skiaGpuContext;
    
    // Orientation tracking properties
    private UIInterfaceOrientation _uiOrientation;
    private UIDeviceOrientation _deviceOrientation;
    private AVCaptureVideoOrientation _videoOrientation;
    private UIImageOrientation _imageOrientation;
    private NSObject _orientationObserver;
    
    public Rotation CurrentRotation { get; private set; } = Rotation.rotate0Degrees;

    public AVCaptureDevice CaptureDevice
    {
        get
        {
            if (_deviceInput == null)
                return null;

            return _deviceInput.Device;
        }
    }

    public NativeCamera(SkiaCamera formsControl)
    {
        FormsControl = formsControl;
        _session = new AVCaptureSession();
        _videoDataOutput = new AVCaptureVideoDataOutput();
        _videoDataOutputQueue = new DispatchQueue("VideoDataOutput", false);

        SetupOrientationObserver();
        InitializeMetalAcceleration();

        Setup();
    }

    /// <summary>
    /// Initialize Metal GPU acceleration for optimal camera frame processing
    /// </summary>
    private void InitializeMetalAcceleration()
    {
        try
        {
            // Get the default Metal device
            _metalDevice = MTLDevice.SystemDefault;
            if (_metalDevice == null)
            {
                Console.WriteLine("[NativeCameraiOS] Metal device not available, falling back to CPU processing");
                return;
            }

            // Create Metal texture cache for CVPixelBuffer to Metal texture conversion
            var result = CVMetalTextureCache.Create(null, null, _metalDevice, null, out _metalTextureCache);
            if (result != CVReturn.Success || _metalTextureCache == null)
            {
                Console.WriteLine($"[NativeCameraiOS] Failed to create Metal texture cache: {result}");
                return;
            }

            // Create Skia GPU context for Metal (using the same pattern as SKMetalViewEnhanced)
            var backendContext = new GRMtlBackendContext
            {
                Device = _metalDevice,
                Queue = _metalDevice.CreateCommandQueue()
            };
            _skiaGpuContext = GRContext.CreateMetal(backendContext);
            if (_skiaGpuContext == null)
            {
                Console.WriteLine("[NativeCameraiOS] Failed to create Skia GPU context");
                return;
            }

            Console.WriteLine("[NativeCameraiOS] Metal GPU acceleration initialized successfully");
        }
        catch (Exception e)
        {
            Console.WriteLine($"[NativeCameraiOS] Metal initialization error: {e}");
            _metalDevice = null;
            _metalTextureCache = null;
            _skiaGpuContext = null;
        }
    }

   



    #region Properties

    public CameraProcessorState State
    {
        get => _state;
        set
        {
            if (_state != value)
            {
                _state = value;
                OnPropertyChanged();
                
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    switch (value)
                    {
                        case CameraProcessorState.Enabled:
                            FormsControl.State = CameraState.On;
                            break;
                        case CameraProcessorState.Error:
                            FormsControl.State = CameraState.Error;
                            break;
                        default:
                            FormsControl.State = CameraState.Off;
                            break;
                    }
                });
            }
        }
    }

    public Action<CapturedImage> PreviewCaptureSuccess { get; set; }
    public Action<CapturedImage> StillImageCaptureSuccess { get; set; }
    public Action<Exception> StillImageCaptureFailed { get; set; }

    #endregion

    #region Setup

    private void Setup()
    {
        try
        {
            SetupHardware();
            State = CameraProcessorState.Enabled;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[NativeCameraiOS] Setup error: {e}");
            State = CameraProcessorState.Error;
        }
    }

    private void SetupOrientationObserver()
    {
        // Initialize orientation values
        _uiOrientation = UIApplication.SharedApplication.StatusBarOrientation;
        _deviceOrientation = UIDevice.CurrentDevice.Orientation;
        _videoOrientation = AVCaptureVideoOrientation.Portrait;
        
        System.Diagnostics.Debug.WriteLine($"[CAMERA SETUP] Initial orientations - UI: {_uiOrientation}, Device: {_deviceOrientation}, Video: {_videoOrientation}");
        
        // Set up orientation change observer
        _orientationObserver = NSNotificationCenter.DefaultCenter.AddObserver(
            UIDevice.OrientationDidChangeNotification, 
            (notification) =>
            {
                System.Diagnostics.Debug.WriteLine($"[CAMERA ORIENTATION] Device orientation changed notification received");
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    UpdateOrientationFromMainThread();
                    // Also update the SkiaCamera's DeviceRotation to ensure both systems are in sync
                    var deviceOrientation = UIDevice.CurrentDevice.Orientation;
                    var rotation = 0;
                    switch (deviceOrientation)
                    {
                        case UIDeviceOrientation.Portrait:
                            rotation = 0;
                            break;
                        case UIDeviceOrientation.LandscapeLeft:
                            rotation = 90;
                            break;
                        case UIDeviceOrientation.PortraitUpsideDown:
                            rotation = 180;
                            break;
                        case UIDeviceOrientation.LandscapeRight:
                            rotation = 270;
                            break;
                        default:
                            rotation = 0;
                            break;
                    }
                    System.Diagnostics.Debug.WriteLine($"[CAMERA ORIENTATION] Setting SkiaCamera DeviceRotation to {rotation} degrees");
                    FormsControl.DeviceRotation = rotation;
                });
            });
    }

    private void SetupHardware()
    {
        _session.BeginConfiguration();
        _cameraUnitInitialized = false;

        // Set session preset
        if (UIDevice.CurrentDevice.UserInterfaceIdiom == UIUserInterfaceIdiom.Pad)
        {
            _session.SessionPreset = AVCaptureSession.PresetHigh;
        }
        else
        {
            _session.SessionPreset = AVCaptureSession.PresetInputPriority;
        }

        // Configure camera position
        var cameraPosition = FormsControl.Facing == CameraPosition.Selfie 
            ? AVCaptureDevicePosition.Front 
            : AVCaptureDevicePosition.Back;

        AVCaptureDevice videoDevice = null;
        
        if (UIDevice.CurrentDevice.CheckSystemVersion(13, 0) && FormsControl.Type == CameraType.Max)
        {
            videoDevice = AVCaptureDevice.GetDefaultDevice(AVCaptureDeviceType.BuiltInTripleCamera, AVMediaTypes.Video, cameraPosition);
        }

        if (videoDevice == null)
        {
            if (UIDevice.CurrentDevice.CheckSystemVersion(10, 2) && FormsControl.Type == CameraType.Max)
            {
                videoDevice = AVCaptureDevice.GetDefaultDevice(AVCaptureDeviceType.BuiltInDualCamera, AVMediaTypes.Video, cameraPosition);
            }

            if (videoDevice == null)
            {
                var videoDevices = AVCaptureDevice.DevicesWithMediaType(AVMediaTypes.Video.GetConstant());
                videoDevice = videoDevices.FirstOrDefault(d => d.Position == cameraPosition);
                
                if (videoDevice == null)
                {
                    State = CameraProcessorState.Error;
                    _session.CommitConfiguration();
                    return;
                }
            }
        }

        var allFormats = videoDevice.Formats.ToList();
        AVCaptureDeviceFormat format = null;
        
        if (UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
        {
            format = allFormats.Where(x => x.MultiCamSupported)
                .OrderByDescending(x => x.HighResolutionStillImageDimensions.Width)
                .FirstOrDefault();
        }

        if (format == null)
        {
            format = allFormats.OrderByDescending(x => x.HighResolutionStillImageDimensions.Width)
                .FirstOrDefault();
        }

        NSError error;
        if (videoDevice.LockForConfiguration(out error))
        {
            if (videoDevice.SmoothAutoFocusSupported)
                videoDevice.SmoothAutoFocusEnabled = true;
                
            videoDevice.ActiveFormat = format;
            
            // Ensure exposure is set to continuous auto exposure during setup
            if (videoDevice.IsExposureModeSupported(AVCaptureExposureMode.ContinuousAutoExposure))
            {
                videoDevice.ExposureMode = AVCaptureExposureMode.ContinuousAutoExposure;
                System.Diagnostics.Debug.WriteLine($"[CAMERA SETUP] Set initial exposure mode to ContinuousAutoExposure");
            }
            
            // Reset exposure bias to neutral
            if (videoDevice.MinExposureTargetBias != videoDevice.MaxExposureTargetBias)
            {
                videoDevice.SetExposureTargetBias(0, null);
                System.Diagnostics.Debug.WriteLine($"[CAMERA SETUP] Reset exposure bias to 0");
            }
            
            videoDevice.UnlockForConfiguration();
        }

        while (_session.Inputs.Any())
        {
            _session.RemoveInput(_session.Inputs[0]);
        }

        _deviceInput = new AVCaptureDeviceInput(videoDevice, out error);
        if (error != null)
        {
            Console.WriteLine($"Could not create video device input: {error.LocalizedDescription}");
            _session.CommitConfiguration();
            State = CameraProcessorState.Error;
            return;
        }

        _session.AddInput(_deviceInput);

        var dictionary = new NSMutableDictionary();
        dictionary[AVVideo.CodecKey] = new NSNumber((int)AVVideoCodec.JPEG);
        _stillImageOutput = new AVCaptureStillImageOutput()
        {
            OutputSettings = new NSDictionary()
        };
        _stillImageOutput.HighResolutionStillImageOutputEnabled = true;
        _session.AddOutput(_stillImageOutput);

        if (_session.CanAddOutput(_videoDataOutput))
        {
            _session.AddOutput(_videoDataOutput);
            _videoDataOutput.AlwaysDiscardsLateVideoFrames = true;
            _videoDataOutput.WeakVideoSettings = new NSDictionary(CVPixelBuffer.PixelFormatTypeKey, 
                CVPixelFormatType.CV32BGRA);
            _videoDataOutput.SetSampleBufferDelegate(this, _videoDataOutputQueue);
            
            // Set initial video orientation from the connection
            var videoConnection = _videoDataOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());
            if (videoConnection != null && videoConnection.SupportsVideoOrientation)
            {
                _videoOrientation = videoConnection.VideoOrientation;
                System.Diagnostics.Debug.WriteLine($"[CAMERA SETUP] Initial video orientation: {_videoOrientation}");
            }
        }
        else
        {
            Console.WriteLine("Could not add video data output to the session");
            _session.CommitConfiguration();
            State = CameraProcessorState.Error;
            return;
        }

        _flashSupported = videoDevice.FlashAvailable;

        var focalLengths = new List<float>();
        //var physicalFocalLength = 4.15f;
        //focalLengths.Add(physicalFocalLength);

        var cameraUnit = new CameraUnit
        {
            Id = videoDevice.UniqueID,
            Facing = FormsControl.Facing,
            FocalLengths = focalLengths,
            FieldOfView = videoDevice.ActiveFormat.VideoFieldOfView,
            Meta = FormsControl.CreateMetadata()
        };

        //other data will be filled when camera starts working..

        FormsControl.CameraDevice = cameraUnit;

        var formatDescription = videoDevice.ActiveFormat.FormatDescription as CMVideoFormatDescription;
        if (formatDescription != null)
        {
            var dimensions = formatDescription.Dimensions;
            FormsControl.SetRotatedContentSize(new SKSize(dimensions.Width, dimensions.Height), 0);
        }

        _session.CommitConfiguration();

        UpdateDetectOrientation();
    }

 

    #endregion

    #region INativeCamera Implementation

    public void Start()
    {
        if (State == CameraProcessorState.Enabled && _session.Running)
            return;

        try
        {
            _session.StartRunning();
            State = CameraProcessorState.Enabled;
            
            MainThread.BeginInvokeOnMainThread(() =>
            {
                DeviceDisplay.Current.KeepScreenOn = true;
            });
        }
        catch (Exception e)
        {
            Console.WriteLine($"[NativeCameraiOS] Start error: {e}");
            State = CameraProcessorState.Error;
        }
    }

    public void Stop(bool force = false)
    {
        SetCapture(null);

        // Clear Metal frame data
        lock (_lockMetalFrame)
        {
            _latestMetalFrame?.Dispose();
            _oldMetalFrame?.Dispose();
            _latestMetalFrame = null;
            _oldMetalFrame = null;
        }

        if (State == CameraProcessorState.None && !force)
            return;

        if (State != CameraProcessorState.Enabled && !force)
            return; //avoid spam

        try
        {
            _session.StopRunning();
            State = CameraProcessorState.None;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[NativeCameraiOS] Stop error: {e}");
            State = CameraProcessorState.Error;
        }
    }

    public void TurnOnFlash()
    {
        if (!_flashSupported || _deviceInput?.Device == null)
            return;

        Console.WriteLine("[NativeCameraiOS] TurnOnFlash");

        NSError error;
        if (_deviceInput.Device.LockForConfiguration(out error))
        {
            try
            {
                if (_deviceInput.Device.HasTorch)
                {
                    _deviceInput.Device.TorchMode = AVCaptureTorchMode.On;
                }
                if (_deviceInput.Device.HasFlash)
                {
                    _deviceInput.Device.FlashMode = AVCaptureFlashMode.On;
                }
            }
            finally
            {
                _deviceInput.Device.UnlockForConfiguration();
            }
        }
    }

    public void TurnOffFlash()
    {
        if (!_flashSupported || _deviceInput?.Device == null)
            return;

        NSError error;
        if (_deviceInput.Device.LockForConfiguration(out error))
        {
            try
            {
                if (_deviceInput.Device.HasTorch)
                {
                    _deviceInput.Device.TorchMode = AVCaptureTorchMode.Off;
                }
                if (_deviceInput.Device.HasFlash)
                {
                    _deviceInput.Device.FlashMode = AVCaptureFlashMode.Off;
                }
            }
            finally
            {
                _deviceInput.Device.UnlockForConfiguration();
            }
        }
    }

    public void SetZoom(float zoom)
    {
        if (_deviceInput?.Device == null)
            return;

        _zoomScale = zoom;

        NSError error;
        if (_deviceInput.Device.LockForConfiguration(out error))
        {
            try
            {
                var clampedZoom = (nfloat)Math.Max(_deviceInput.Device.MinAvailableVideoZoomFactor,
                    Math.Min(zoom, _deviceInput.Device.MaxAvailableVideoZoomFactor));

                _deviceInput.Device.VideoZoomFactor = clampedZoom;
            }
            finally
            {
                _deviceInput.Device.UnlockForConfiguration();
            }
        }
    }

    /// <summary>
    /// Sets manual exposure settings for the camera
    /// </summary>
    /// <param name="iso">ISO sensitivity value</param>
    /// <param name="shutterSpeed">Shutter speed in seconds</param>
    public bool SetManualExposure(float iso, float shutterSpeed)
    {
        if (_deviceInput?.Device == null)
            return false;

        NSError error;
        if (_deviceInput.Device.LockForConfiguration(out error))
        {
            try
            {
                if (_deviceInput.Device.IsExposureModeSupported(AVCaptureExposureMode.Custom))
                {
                    var duration = CMTime.FromSeconds(shutterSpeed, 1000000000);
                    _deviceInput.Device.LockExposure(duration, iso, null);

                    System.Diagnostics.Debug.WriteLine($"[iOS MANUAL] Set ISO: {iso}, Shutter: {shutterSpeed}s");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[iOS MANUAL] Custom exposure mode not supported");
                }
            }
            finally
            {
                _deviceInput.Device.UnlockForConfiguration();
            }
        }

        return false;
    }

    /// <summary>
    /// Sets the camera to automatic exposure mode
    /// </summary>
    public void SetAutoExposure()
    {
        if (_deviceInput?.Device == null)
            return;

        NSError error;
        if (_deviceInput.Device.LockForConfiguration(out error))
        {
            try
            {
                if (_deviceInput.Device.IsExposureModeSupported(AVCaptureExposureMode.ContinuousAutoExposure))
                {
                    _deviceInput.Device.ExposureMode = AVCaptureExposureMode.ContinuousAutoExposure;
                    System.Diagnostics.Debug.WriteLine("[iOS AUTO] Set to ContinuousAutoExposure mode");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[iOS AUTO] ContinuousAutoExposure mode not supported");
                }
            }
            finally
            {
                _deviceInput.Device.UnlockForConfiguration();
            }
        }
    }

    /// <summary>
    /// Gets the manual exposure capabilities and recommended settings for the camera
    /// </summary>
    /// <returns>Camera manual exposure range information</returns>
    public CameraManualExposureRange GetExposureRange()
    {
        if (_deviceInput?.Device == null)
        {
            return new CameraManualExposureRange(0, 0, 0, 0, false, null);
        }

        try
        {
            bool isSupported = _deviceInput.Device.IsExposureModeSupported(AVCaptureExposureMode.Custom);

            if (!isSupported)
            {
                return new CameraManualExposureRange(0, 0, 0, 0, false, null);
            }

            float minISO = _deviceInput.Device.ActiveFormat.MinISO;
            float maxISO = _deviceInput.Device.ActiveFormat.MaxISO;
            float minShutter = (float)_deviceInput.Device.ActiveFormat.MinExposureDuration.Seconds;
            float maxShutter = (float)_deviceInput.Device.ActiveFormat.MaxExposureDuration.Seconds;

            var baselines = new CameraExposureBaseline[]
            {
                new CameraExposureBaseline(100, 1.0f/60.0f, "Indoor", "Office/bright indoor lighting"),
                new CameraExposureBaseline(400, 1.0f/30.0f, "Mixed", "Dim indoor/overcast outdoor"),
                new CameraExposureBaseline(800, 1.0f/15.0f, "Low Light", "Evening/dark indoor")
            };

            System.Diagnostics.Debug.WriteLine($"[iOS RANGE] ISO: {minISO}-{maxISO}, Shutter: {minShutter}-{maxShutter}s");

            return new CameraManualExposureRange(minISO, maxISO, minShutter, maxShutter, true, baselines);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[iOS RANGE] Error: {ex.Message}");
            return new CameraManualExposureRange(0, 0, 0, 0, false, null);
        }
    }

    public void ApplyDeviceOrientation(int orientation)
    {
        UpdateOrientationFromMainThread();
    }

    public void TakePicture()
    {
        if (_isCapturingStill || _stillImageOutput == null)
            return;

        Task.Run(async () =>
        {
            try
            {
                _isCapturingStill = true;

                var status = PHPhotoLibrary.AuthorizationStatus;
                if (status != PHAuthorizationStatus.Authorized)
                {
                    status = await PHPhotoLibrary.RequestAuthorizationAsync();
                    if (status != PHAuthorizationStatus.Authorized)
                    {
                        StillImageCaptureFailed?.Invoke(new UnauthorizedAccessException("Photo library access denied"));
                        return;
                    }
                }

                var videoConnection = _stillImageOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());
                var sampleBuffer = await _stillImageOutput.CaptureStillImageTaskAsync(videoConnection);
                var jpegData = AVCaptureStillImageOutput.JpegStillToNSData(sampleBuffer);

                using var uiImage = UIImage.LoadFromData(jpegData);
                var skImage = uiImage.ToSKImage();

                var capturedImage = new CapturedImage()
                {
                    Facing = FormsControl.Facing,
                    Time = DateTime.UtcNow,
                    Image = skImage,
                    Orientation = FormsControl.DeviceRotation
                };

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    StillImageCaptureSuccess?.Invoke(capturedImage);
                });
            }
            catch (Exception e)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    StillImageCaptureFailed?.Invoke(e);
                });
            }
            finally
            {
                _isCapturingStill = false;
            }
        });
    }

    /// <summary>
    /// Returns GPU-backed SKImage for optimal renderer performance.
    /// Uses Metal textures when available, falls back to CPU processing if needed.
    /// </summary>
    public SKImage GetPreviewImage()
    {
        // Atomic protection: prevent multiple calls
        if (System.Threading.Interlocked.CompareExchange(ref _gettingPreviewImage, 1, 0) != 0)
        {
            System.Diagnostics.Debug.WriteLine("[GetPreviewImage] SKIPPED - already getting preview");
            return null;
        }

        try
        {
            // PRIORITY 1: Return pre-processed GPU-backed image from Metal pipeline
            lock (_lockMetalFrame)
            {
                if (_latestMetalFrame?.PreprocessedImage != null)
                {
                    var gpuImage = _latestMetalFrame.PreprocessedImage;
                    _latestMetalFrame.PreprocessedImage = null; // Transfer ownership

                    // Clean up the frame data
                    _oldMetalFrame?.Dispose();
                    _oldMetalFrame = _latestMetalFrame;
                    _latestMetalFrame = null;

                    System.Diagnostics.Debug.WriteLine($"[GetPreviewImage] SUCCESS - returned GPU-backed image {gpuImage.Width}x{gpuImage.Height}");
                    return gpuImage;
                }
            }

            // PRIORITY 2: Check if we have a ready CPU-processed preview (fallback)
            lock (_lockPreview)
            {
                if (_preview?.Image != null)
                {
                    var preview = _preview.Image;
                    _preview.Image = null; // Transfer ownership
                    _preview = null;
                    System.Diagnostics.Debug.WriteLine("[GetPreviewImage] SUCCESS - returned CPU fallback preview");
                    return preview;
                }
            }

            System.Diagnostics.Debug.WriteLine("[GetPreviewImage] No frame available");
            return null;
        }
        finally
        {
            // Always reset the atomic flag
            System.Threading.Interlocked.Exchange(ref _gettingPreviewImage, 0);
        }
    }

    // Add this field to prevent multiple GetPreviewImage calls
    private volatile int _gettingPreviewImage = 0;

    public async Task<string> SaveJpgStreamToGallery(Stream stream, string filename, double cameraSavedRotation, string album)
    {
        try
        {
            var data = NSData.FromStream(stream);
            
            bool complete = false;
            string resultPath = null;

            PHPhotoLibrary.SharedPhotoLibrary.PerformChanges(() =>
            {
                var options = new PHAssetResourceCreationOptions
                {
                    OriginalFilename = filename
                };

                var creationRequest = PHAssetCreationRequest.CreationRequestForAsset();
                creationRequest.AddResource(PHAssetResourceType.Photo, data, options);

            }, (success, error) =>
            {
                if (success)
                {
                    resultPath = filename;
                }
                else
                {
                    Console.WriteLine($"SaveJpgStreamToGallery error: {error}");
                }
                complete = true;
            });

            while (!complete)
            {
                await Task.Delay(10);
            }

            return resultPath;
        }
        catch (Exception e)
        {
            Console.WriteLine($"SaveJpgStreamToGallery error: {e}");
            return null;
        }
    }

    #endregion

    /// <summary>
    /// Gets current live exposure settings from AVCaptureDevice in auto exposure mode
    /// These properties update dynamically as the camera adjusts exposure automatically
    /// </summary>
    private (float iso, float aperture, float shutterSpeed) GetLiveExposureSettings()
    {
        if (CaptureDevice == null)
            return (100f, 1.8f, 1f / 60f);

        try
        {
            // These properties are observable and change dynamically in auto exposure mode
            var currentISO = CaptureDevice.ISO;                          // Real-time ISO
            var currentAperture = CaptureDevice.LensAperture;            // Fixed on iPhone (f/1.8, f/2.8, etc)
            var exposureDuration = CaptureDevice.ExposureDuration;       // Real-time shutter speed
            var currentShutter = (float)exposureDuration.Seconds;

            return (currentISO, currentAperture, currentShutter);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[iOS Exposure Error] {ex.Message}");
            return (100f, 1.8f, 1f / 60f);
        }
    }

    #region AVCaptureVideoDataOutputSampleBufferDelegate

    /// <summary>
    /// High-performance frame processing using Metal GPU acceleration.
    /// Creates GPU-backed SKImages directly from CVPixelBuffer without CPU memory copying.
    /// </summary>
    [Export("captureOutput:didOutputSampleBuffer:fromConnection:")]
    public void DidOutputSampleBuffer(AVCaptureOutput captureOutput, CMSampleBuffer sampleBuffer, AVCaptureConnection connection)
    {
        if (FormsControl == null || _isCapturingStill || State != CameraProcessorState.Enabled)
            return;

        // THROTTLING: Only skip if previous frame is still being processed (prevents thread overwhelm)
        if (_isProcessingFrame)
        {
            _skippedFrameCount++;
            return;
        }

        _isProcessingFrame = true;
        _processedFrameCount++;

        // Log stats every 300 frames
        if (_processedFrameCount % 300 == 0)
        {
            System.Diagnostics.Debug.WriteLine($"[NativeCameraiOS] Frame stats - Processed: {_processedFrameCount}, Skipped: {_skippedFrameCount}");
        }

        bool hasFrame = false;
        try
        {
            using var pixelBuffer = sampleBuffer.GetImageBuffer() as CVPixelBuffer;
            if (pixelBuffer == null)
                return;

            // Update camera metadata
            UpdateCameraMetadata(sampleBuffer);

            // PRIORITY 1: Try Metal GPU acceleration path
            if (_metalDevice != null && _metalTextureCache != null && _skiaGpuContext != null)
            {
                var metalFrame = ProcessFrameWithMetal(pixelBuffer);
                if (metalFrame != null)
                {
                    SetMetalFrame(metalFrame);
                    hasFrame = true;
                    System.Diagnostics.Debug.WriteLine("[DidOutputSampleBuffer] SUCCESS - Metal GPU path");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[DidOutputSampleBuffer] Metal processing failed, falling back to CPU");
                }
            }

            // PRIORITY 2: Fallback to CPU processing if Metal failed
            if (!hasFrame)
            {
                var cpuFrame = ProcessFrameWithCPU(pixelBuffer);
                if (cpuFrame != null)
                {
                    SetCapture(cpuFrame);
                    hasFrame = true;
                    System.Diagnostics.Debug.WriteLine("[DidOutputSampleBuffer] SUCCESS - CPU fallback path");
                }
            }

            if (hasFrame)
            {
                FormsControl.UpdatePreview();
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"[NativeCameraiOS] Frame processing error: {e}");
        }
        finally
        {
            // IMPORTANT: Always reset processing flag
            _isProcessingFrame = false;
        }
    }

    CapturedImage _kill;

    /// <summary>
    /// Process camera frame using Metal GPU acceleration for optimal performance
    /// </summary>
    private MetalFrameData ProcessFrameWithMetal(CVPixelBuffer pixelBuffer)
    {
        try
        {
            var width = (int)pixelBuffer.Width;
            var height = (int)pixelBuffer.Height;

            // Create Metal texture from CVPixelBuffer
            var result = CVMetalTextureCache.CreateTextureFromImage(
                null, _metalTextureCache, pixelBuffer, null,
                MTLPixelFormat.BGRA8Unorm, width, height, 0, out var metalTexture);

            if (result != CVReturn.Success || metalTexture == null)
            {
                System.Diagnostics.Debug.WriteLine($"[ProcessFrameWithMetal] Failed to create Metal texture: {result}");
                return null;
            }

            // Create GPU-backed SKImage from Metal texture
            var texture = metalTexture.Texture;
            if (texture == null)
            {
                metalTexture?.Dispose();
                return null;
            }

            // Create GRBackendTexture from Metal texture (using the same pattern as SKMetalViewEnhanced)
            var textureInfo = new GRMtlTextureInfo(texture);
            var backendTexture = new GRBackendRenderTarget(width, height, 1, textureInfo);

            // Create GPU surface from render target and capture as SKImage
            var imageInfo = new SKImageInfo(width, height, SKColorType.Bgra8888, SKAlphaType.Premul);
            using var surface = SKSurface.Create(_skiaGpuContext, backendTexture, GRSurfaceOrigin.TopLeft, imageInfo);
            if (surface?.Canvas == null)
            {
                metalTexture?.Dispose();
                return null;
            }

            // Capture the current texture content as SKImage
            var skImage = surface.Snapshot();
            if (skImage == null)
            {
                metalTexture?.Dispose();
                return null;
            }

            // Apply rotation if needed (on GPU)
            SKImage finalImage;
            if (CurrentRotation != Rotation.rotate0Degrees)
            {
                finalImage = CreateRotatedImageOnGPU(skImage, CurrentRotation);
                skImage.Dispose(); // Dispose original
            }
            else
            {
                finalImage = skImage;
            }

            return new MetalFrameData
            {
                MetalTexture = metalTexture,
                Width = width,
                Height = height,
                Time = DateTime.UtcNow,
                CurrentRotation = CurrentRotation,
                Facing = FormsControl.Facing,
                Orientation = (int)CurrentRotation,
                PreprocessedImage = finalImage // GPU-backed, pre-rotated SKImage
            };
        }
        catch (Exception e)
        {
            Console.WriteLine($"[ProcessFrameWithMetal] Error: {e}");
            return null;
        }
    }

    /// <summary>
    /// Fallback CPU processing method (similar to original implementation but optimized)
    /// </summary>
    private CapturedImage ProcessFrameWithCPU(CVPixelBuffer pixelBuffer)
    {
        try
        {
            pixelBuffer.Lock(CVPixelBufferLock.ReadOnly);
            try
            {
                var width = (int)pixelBuffer.Width;
                var height = (int)pixelBuffer.Height;
                var bytesPerRow = (int)pixelBuffer.BytesPerRow;
                var baseAddress = pixelBuffer.BaseAddress;

                // Create SKImage directly from pixel buffer (avoid copying when possible)
                var info = new SKImageInfo(width, height, SKColorType.Bgra8888, SKAlphaType.Premul);
                var skImage = SKImage.FromPixels(info, baseAddress, bytesPerRow);

                // Apply rotation if needed
                SKImage finalImage;
                if (CurrentRotation != Rotation.rotate0Degrees)
                {
                    using var bitmap = SKBitmap.FromImage(skImage);
                    using var rotatedBitmap = HandleOrientation(bitmap, (double)CurrentRotation);
                    finalImage = SKImage.FromBitmap(rotatedBitmap);
                    skImage.Dispose();
                }
                else
                {
                    finalImage = skImage;
                }

                return new CapturedImage
                {
                    Facing = FormsControl.Facing,
                    Time = DateTime.UtcNow,
                    Image = finalImage,
                    Orientation = FormsControl.DeviceRotation
                };
            }
            finally
            {
                pixelBuffer.Unlock(CVPixelBufferLock.ReadOnly);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"[ProcessFrameWithCPU] Error: {e}");
            return null;
        }
    }

    /// <summary>
    /// Create rotated image using GPU operations for better performance
    /// </summary>
    private SKImage CreateRotatedImageOnGPU(SKImage sourceImage, Rotation rotation)
    {
        try
        {
            if (rotation == Rotation.rotate0Degrees)
                return sourceImage;

            var (newWidth, newHeight) = rotation == Rotation.rotate90Degrees || rotation == Rotation.rotate270Degrees
                ? (sourceImage.Height, sourceImage.Width)
                : (sourceImage.Width, sourceImage.Height);

            // Create GPU surface for rotation
            var imageInfo = new SKImageInfo(newWidth, newHeight, SKColorType.Bgra8888, SKAlphaType.Premul);
            using var surface = SKSurface.Create(_skiaGpuContext, true, imageInfo);
            if (surface?.Canvas == null)
                return sourceImage; // Fallback to original if GPU surface creation fails

            var canvas = surface.Canvas;
            canvas.Clear(SKColors.Transparent);

            // Apply rotation transformation
            switch (rotation)
            {
                case Rotation.rotate90Degrees:
                    canvas.Translate(newWidth, 0);
                    canvas.RotateDegrees(90);
                    break;
                case Rotation.rotate180Degrees:
                    canvas.Translate(newWidth, newHeight);
                    canvas.RotateDegrees(180);
                    break;
                case Rotation.rotate270Degrees:
                    canvas.Translate(0, newHeight);
                    canvas.RotateDegrees(270);
                    break;
            }

            canvas.DrawImage(sourceImage, 0, 0);
            canvas.Flush();

            return surface.Snapshot();
        }
        catch (Exception e)
        {
            Console.WriteLine($"[CreateRotatedImageOnGPU] Error: {e}");
            return sourceImage; // Return original on error
        }
    }

    /// <summary>
    /// Update camera metadata from sample buffer
    /// </summary>
    private void UpdateCameraMetadata(CMSampleBuffer sampleBuffer)
    {
        try
        {
            var (iso, aperture, shutterSpeed) = GetLiveExposureSettings();

            var attachments = sampleBuffer.GetAttachments(CMAttachmentMode.ShouldPropagate);
            var exif = attachments["{Exif}"] as NSDictionary;
            var focal = exif["FocalLength"].ToString().ToFloat();

            if (!_cameraUnitInitialized)
            {
                _cameraUnitInitialized = true;

                var focals = new List<float>();
                var focal35mm = exif["FocalLenIn35mmFilm"].ToString().ToFloat();
                var name = exif["LensModel"].ToString();
                var lenses = exif["LensSpecification "] as NSDictionary;
                if (lenses != null)
                {
                    foreach (var lens in lenses)
                    {
                        var add = lens.ToString().ToDouble();
                        focals.Add((float)add);
                    }
                }
                else
                {
                    focals.Add((float)focal);
                }

                var unit = FormsControl.CameraDevice;
                unit.Id = name;
                unit.SensorCropFactor = focal35mm / focal;
                unit.FocalLengths = focals;
                unit.PixelXDimension = exif["PixelXDimension"].ToString().ToFloat();
                unit.PixelYDimension = exif["PixelYDimension"].ToString().ToFloat();
                unit.FocalLength = focal;

                var info = _deviceInput.Device.ActiveFormat;
                float aspectH = unit.PixelXDimension / unit.PixelYDimension;
                float fovH = info.VideoFieldOfView;
                float fovV = fovH / aspectH;

                var sensorWidth = (float)(2 * unit.FocalLength * Math.Tan(fovH * Math.PI / 2.0f * 180));
                var sensorHeight = (float)(2 * unit.FocalLength * Math.Tan(fovV * Math.PI / 2.0f * 180));

                unit.SensorHeight = sensorHeight;
                unit.SensorWidth = sensorWidth;
                unit.FieldOfView = fovH;
            }

            FormsControl.CameraDevice.Meta.FocalLength = focal;
            FormsControl.CameraDevice.Meta.ISO = (int)iso;
            FormsControl.CameraDevice.Meta.Aperture = aperture;
            FormsControl.CameraDevice.Meta.Shutter = shutterSpeed;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[UpdateCameraMetadata] Error: {e}");
        }
    }

    /// <summary>
    /// Set Metal frame data with proper disposal of old frames
    /// </summary>
    void SetMetalFrame(MetalFrameData metalFrame)
    {
        lock (_lockMetalFrame)
        {
            // Dispose old Metal frame data
            _oldMetalFrame?.Dispose();
            _oldMetalFrame = _latestMetalFrame;
            _latestMetalFrame = metalFrame;
        }
    }

    void SetCapture(CapturedImage capturedImage)
    {
        lock (_lockPreview)
        {
            // Apple's recommended pattern: Keep only the latest frame
            // Dispose the old preview immediately if we have a new one
            if (_preview != null && capturedImage != null)
            {
                _preview.Dispose();
                _preview = null;
            }

            // Dispose any queued frame
            _kill?.Dispose();
            _kill = _preview;
            _preview = capturedImage;
        }
    }

    #endregion



    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler PropertyChanged;

    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion

    #region Orientation Handling

    public SKBitmap HandleOrientation(SKBitmap bitmap, double sensor)
    {
        SKBitmap rotated;
        switch (sensor)
        {
            case 180:
                using (var surface = new SKCanvas(bitmap))
                {
                    surface.RotateDegrees(180, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
                    surface.DrawBitmap(bitmap.Copy(), 0, 0);
                }
                return bitmap;

            case 270: //iphone on the right side
                rotated = new SKBitmap(bitmap.Height, bitmap.Width);
                using (var surface = new SKCanvas(rotated))
                {
                    surface.Translate(0, rotated.Height);
                    surface.RotateDegrees(270);
                    surface.DrawBitmap(bitmap, 0, 0);
                }
                return rotated;

            case 90: // iphone on the left side
                rotated = new SKBitmap(bitmap.Height, bitmap.Width);
                using (var surface = new SKCanvas(rotated))
                {
                    surface.Translate(rotated.Width, 0);
                    surface.RotateDegrees(90);
                    surface.DrawBitmap(bitmap, 0, 0);
                }
                return rotated;

            default:
                return bitmap;
        }
    }

    public void UpdateOrientationFromMainThread()
    {
        _uiOrientation = UIApplication.SharedApplication.StatusBarOrientation;
        _deviceOrientation = UIDevice.CurrentDevice.Orientation;
        UpdateDetectOrientation();
    }

    public void UpdateDetectOrientation()
    {
        if (_videoDataOutput?.Connections?.Any() == true)
        {
            // Get current video orientation from connection
            var videoConnection = _videoDataOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());
            if (videoConnection != null && videoConnection.SupportsVideoOrientation)
            {
                _videoOrientation = videoConnection.VideoOrientation;
            }
            
            CurrentRotation = GetRotation(
                _uiOrientation,
                _videoOrientation,
                _deviceInput?.Device?.Position ?? AVCaptureDevicePosition.Back);

            switch (_uiOrientation)
            {
                case UIInterfaceOrientation.Portrait:
                    _imageOrientation = UIImageOrientation.Right;
                    break;
                case UIInterfaceOrientation.PortraitUpsideDown:
                    _imageOrientation = UIImageOrientation.Left;
                    break;
                case UIInterfaceOrientation.LandscapeLeft:
                    _imageOrientation = UIImageOrientation.Up;
                    break;
                case UIInterfaceOrientation.LandscapeRight:
                    _imageOrientation = UIImageOrientation.Down;
                    break;
                default:
                    _imageOrientation = UIImageOrientation.Up;
                    break;
            }

            System.Diagnostics.Debug.WriteLine($"[UpdateDetectOrientation]: rotation: {CurrentRotation}, orientation: {_imageOrientation}, device: {_deviceInput?.Device?.Position}, video: {_videoOrientation}, ui:{_uiOrientation}");
        }
    }

    public Rotation GetRotation(
        UIInterfaceOrientation interfaceOrientation,
        AVCaptureVideoOrientation videoOrientation,
        AVCaptureDevicePosition cameraPosition)
    {
        /*
         Calculate the rotation between the videoOrientation and the interfaceOrientation.
         The direction of the rotation depends upon the camera position.
         */

        switch (videoOrientation)
        {
            case AVCaptureVideoOrientation.Portrait:
                switch (interfaceOrientation)
                {
                    case UIInterfaceOrientation.LandscapeRight:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate90Degrees;
                        }
                        else
                        {
                            return Rotation.rotate270Degrees;
                        }

                    case UIInterfaceOrientation.LandscapeLeft:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate270Degrees;
                        }
                        else
                        {
                            return Rotation.rotate90Degrees;
                        }

                    case UIInterfaceOrientation.Portrait:
                        return Rotation.rotate0Degrees;

                    case UIInterfaceOrientation.PortraitUpsideDown:
                        return Rotation.rotate180Degrees;

                    default:
                        return Rotation.rotate0Degrees;
                }

            case AVCaptureVideoOrientation.PortraitUpsideDown:
                switch (interfaceOrientation)
                {
                    case UIInterfaceOrientation.LandscapeRight:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate270Degrees;
                        }
                        else
                        {
                            return Rotation.rotate90Degrees;
                        }

                    case UIInterfaceOrientation.LandscapeLeft:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate90Degrees;
                        }
                        else
                        {
                            return Rotation.rotate270Degrees;
                        }

                    case UIInterfaceOrientation.Portrait:
                        return Rotation.rotate180Degrees;

                    case UIInterfaceOrientation.PortraitUpsideDown:
                        return Rotation.rotate0Degrees;

                    default:
                        return Rotation.rotate180Degrees;
                }

            case AVCaptureVideoOrientation.LandscapeRight:
                switch (interfaceOrientation)
                {
                    case UIInterfaceOrientation.LandscapeRight:
                        return Rotation.rotate0Degrees;

                    case UIInterfaceOrientation.LandscapeLeft:
                        return Rotation.rotate180Degrees;

                    case UIInterfaceOrientation.Portrait:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate270Degrees;
                        }
                        else
                        {
                            return Rotation.rotate90Degrees;
                        }

                    case UIInterfaceOrientation.PortraitUpsideDown:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate90Degrees;
                        }
                        else
                        {
                            return Rotation.rotate270Degrees;
                        }

                    default:
                        return Rotation.rotate0Degrees;
                }

            case AVCaptureVideoOrientation.LandscapeLeft:
                switch (interfaceOrientation)
                {
                    case UIInterfaceOrientation.LandscapeLeft:
                        return Rotation.rotate0Degrees;

                    case UIInterfaceOrientation.LandscapeRight:
                        return Rotation.rotate180Degrees;

                    case UIInterfaceOrientation.Portrait:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate90Degrees;
                        }
                        else
                        {
                            return Rotation.rotate270Degrees;
                        }

                    case UIInterfaceOrientation.PortraitUpsideDown:
                        if (cameraPosition == AVCaptureDevicePosition.Front)
                        {
                            return Rotation.rotate270Degrees;
                        }
                        else
                        {
                            return Rotation.rotate90Degrees;
                        }

                    default:
                        return Rotation.rotate0Degrees;
                }

            default:
                return Rotation.rotate0Degrees;
        }
    }

    #endregion

    #region IDisposable

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            Stop();

            _session?.Dispose();
            _videoDataOutput?.Dispose();
            _stillImageOutput?.Dispose();
            _deviceInput?.Dispose();
            _videoDataOutputQueue?.Dispose();

            SetCapture(null);
            _kill?.Dispose();

            // Clean up Metal frame data
            lock (_lockMetalFrame)
            {
                _latestMetalFrame?.Dispose();
                _oldMetalFrame?.Dispose();
                _latestMetalFrame = null;
                _oldMetalFrame = null;
            }

            // Clean up Metal resources
            _skiaGpuContext?.Dispose();
            _metalTextureCache?.Dispose();
            _metalDevice?.Dispose();
            _skiaGpuContext = null;
            _metalTextureCache = null;
            _metalDevice = null;

            // Clean up orientation observer
            if (_orientationObserver != null)
            {
                NSNotificationCenter.DefaultCenter.RemoveObserver(_orientationObserver);
                _orientationObserver?.Dispose();
                _orientationObserver = null;
            }
        }

        base.Dispose(disposing);
    }

    #endregion
}
#endif
